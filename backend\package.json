{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "node-fetch": "^2.7.0", "pg": "^8.16.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/dotenv": "^8.2.3", "@types/express": "^5.0.3", "@types/node": "^24.3.0", "@types/pg": "^8.15.5", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2"}}