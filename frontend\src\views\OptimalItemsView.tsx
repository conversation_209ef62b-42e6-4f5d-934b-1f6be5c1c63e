import { useState, useEffect } from "react";
import type { ApiItem } from "../types/api";

function OptimalItemsView() {
  const [optimalItems, setOptimalItems] = useState<ApiItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch optimal items from API
  useEffect(() => {
    const fetchOptimalItems = async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual optimal items endpoint
        const res = await fetch(`http://localhost:3001/items/`);
        const data = await res.json();
        setOptimalItems(data);
      } catch (err) {
        console.error("Error fetching optimal items:", err);
        // For now, set empty array if endpoint doesn't exist
        setOptimalItems([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOptimalItems();
  }, []);

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-yellow-500 mb-4">
          Optimal Items for Trading
        </h2>
        <p className="text-gray-300 mb-4">
          These items have the best profit margins and trading volume in the marketplace.
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="text-yellow-500 text-lg">Loading optimal items...</div>
        </div>
      ) : optimalItems.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">
            No optimal items available at the moment
          </div>
          <div className="text-gray-500 text-sm">
            Check back later for updated recommendations
          </div>
        </div>
      )}
    </div>
  );
}

export default OptimalItemsView;
